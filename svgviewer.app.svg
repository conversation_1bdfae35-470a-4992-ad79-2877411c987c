<svg width="1200" height="900" viewBox="0 0 1200 900" xmlns="http://www.w3.org/2000/svg">

  <defs>
    <style><![CDATA[
      .title { font: 700 16px/1.2 "Segoe UI","PingFang SC","Microsoft Yahei",Arial; fill:#111827; text-anchor: middle; }
      .sectionTitle { font: 700 14px/1.2 "Segoe UI","PingFang SC","Microsoft Yahei"; fill:#0F172A; text-anchor: middle; }
      .boxText { font: 12px/1.2 "Segoe UI","PingFang SC","Microsoft Yahei"; fill:#374151; text-anchor: middle; }
      .smallText { font: 10px/1.2 "Segoe UI","PingFang SC","Microsoft Yahei"; fill:#4B5563; text-anchor: middle; }
      .dashedBox { fill:none; stroke:#666; stroke-width:1.5; stroke-dasharray:8,4; rx:8; ry:8; }
      .solidBox { fill:#FFFFFF; stroke:#333; stroke-width:1.5; rx:6; ry:6; }
      .methodBox { fill:#F0F9FF; stroke:#0EA5E9; stroke-width:1.5; rx:20; ry:20; }
      .arrow { stroke:#475569; stroke-width:2; fill:none; }
      .thickArrow { stroke:#333; stroke-width:3; fill:none; }
    ]]></style>
    <marker id="arrowHead" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
      <path d="M 0 0 L 10 5 L 0 10 z" fill="#475569"/>
    </marker>
    <marker id="thickArrowHead" viewBox="0 0 10 10" refX="9" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse">
      <path d="M 0 0 L 10 5 L 0 10 z" fill="#333"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text class="title" x="600" y="30">图1.1 研究框架与技术路线图（基于AI与知识库协同的误报治理与响应优化）</text>

  <!-- 第一层：绪论 -->
  <rect class="dashedBox" x="50" y="50" width="700" height="80"/>
  <text class="sectionTitle" x="400" y="75">绪论</text>

  <!-- 绪论四个子模块 -->
  <rect class="solidBox" x="70" y="95" width="160" height="25"/>
  <text class="boxText" x="150" y="112">研究背景与问题场景（SOC告警疲劳）</text>

  <rect class="solidBox" x="240" y="95" width="160" height="25"/>
  <text class="boxText" x="320" y="112">国内外研究现状（误报治理/响应优化）</text>

  <rect class="solidBox" x="410" y="95" width="160" height="25"/>
  <text class="boxText" x="490" y="112">研究问题与假设（FPR↓/MTTR↓/自动化↑/零高风险误处置）</text>

  <rect class="solidBox" x="580" y="95" width="160" height="25"/>
  <text class="boxText" x="660" y="112">指标与口径（FPR、合并/去重、MTTD/MTTR等）</text>

  <!-- 箭头从绪论到理论与方法框架 -->
  <line class="arrow" x1="400" y1="130" x2="400" y2="160" marker-end="url(#arrowHead)"/>

  <!-- 第二层：理论与方法框架 -->
  <rect class="dashedBox" x="50" y="160" width="700" height="80"/>
  <text class="sectionTitle" x="400" y="185">理论与方法框架</text>

  <!-- 三个子模块 -->
  <rect class="solidBox" x="100" y="205" width="160" height="25"/>
  <text class="boxText" x="180" y="222">业务流程再造（BPR）</text>

  <rect class="solidBox" x="300" y="205" width="160" height="25"/>
  <text class="boxText" x="380" y="222">设计科学研究（DSR）</text>

  <rect class="solidBox" x="500" y="205" width="180" height="25"/>
  <text class="boxText" x="590" y="222">统一语义与知识（OCSF / ATT&amp;CK）</text>

  <!-- 箭头从第二层到第三层 -->
  <line class="arrow" x1="400" y1="240" x2="400" y2="270" marker-end="url(#arrowHead)"/>

  <!-- 第三层：流程绩效评价体系构建 -->
  <rect class="dashedBox" x="50" y="270" width="700" height="120"/>
  <text class="sectionTitle" x="400" y="295">流程绩效与结构指标体系构建</text>

  <!-- 左侧：SOC 场景的数据与语义类型 -->
  <rect class="dashedBox" x="80" y="310" width="250" height="70"/>
  <text class="smallText" x="205" y="325">SOC 场景的数据与语义类型</text>

  <!-- 四类数据/知识 -->
  <rect class="solidBox" x="90" y="335" width="60" height="20"/>
  <text class="smallText" x="120" y="347">日志/告警</text>

  <rect class="solidBox" x="155" y="335" width="60" height="20"/>
  <text class="smallText" x="185" y="347">资产/账号</text>

  <rect class="solidBox" x="220" y="335" width="60" height="20"/>
  <text class="smallText" x="250" y="347">威胁情报</text>

  <rect class="solidBox" x="285" y="335" width="60" height="20"/>
  <text class="smallText" x="315" y="347">工单/复盘</text>

  <!-- 下方两个支撑模块 -->
  <rect class="solidBox" x="95" y="360" width="110" height="15"/>
  <text class="smallText" x="150" y="371">规范支撑（OCSF/ATT&amp;CK）</text>

  <rect class="solidBox" x="215" y="360" width="110" height="15"/>
  <text class="smallText" x="270" y="371">治理支撑（制度/流程）</text>

  <!-- 箭头指向右侧 -->
  <line class="arrow" x1="330" y1="345" x2="370" y2="345" marker-end="url(#arrowHead)"/>

  <!-- 右侧：流程绩效与结构指标体系 -->
  <rect class="dashedBox" x="370" y="310" width="300" height="70"/>
  <text class="smallText" x="520" y="325">流程绩效与结构指标体系</text>

  <!-- 六项核心指标 -->
  <rect class="solidBox" x="380" y="335" width="60" height="20"/>
  <text class="smallText" x="410" y="347">FPR</text>

  <rect class="solidBox" x="445" y="335" width="70" height="20"/>
  <text class="smallText" x="480" y="347">合并/去重</text>

  <rect class="solidBox" x="520" y="335" width="70" height="20"/>
  <text class="smallText" x="555" y="347">MTTD/MTTR</text>

  <rect class="solidBox" x="595" y="335" width="70" height="20"/>
  <text class="smallText" x="630" y="347">自动化覆盖</text>

  <rect class="solidBox" x="380" y="360" width="80" height="20"/>
  <text class="smallText" x="420" y="372">自动化成功率</text>

  <rect class="solidBox" x="465" y="360" width="90" height="20"/>
  <text class="smallText" x="510" y="372">升级/回退比例</text>

  <rect class="solidBox" x="560" y="360" width="110" height="20"/>
  <text class="smallText" x="615" y="372">证据链完整性</text>

  <!-- 箭头从第三层到第四层 -->
  <line class="arrow" x1="400" y1="390" x2="400" y2="420" marker-end="url(#arrowHead)"/>

  <!-- 第四层：流程构件与原型（证据链/分流/编排） -->
  <rect class="dashedBox" x="50" y="420" width="700" height="150"/>
  <text class="sectionTitle" x="400" y="445">流程构件与原型（证据链 / 分流优先级 / 编排与治理）</text>

  <!-- 左侧：多源日志与告警 -->
  <rect class="solidBox" x="80" y="460" width="100" height="40"/>
  <text class="smallText" x="130" y="475">多源日志</text>
  <text class="smallText" x="130" y="490">与告警</text>

  <!-- 箭头指向事件标准化（OCSF） -->
  <line class="arrow" x1="180" y1="480" x2="220" y2="480" marker-end="url(#arrowHead)"/>

  <!-- 事件标准化 -->
  <rect class="solidBox" x="220" y="460" width="100" height="40"/>
  <text class="smallText" x="270" y="475">事件标准化</text>
  <text class="smallText" x="270" y="490">（OCSF）</text>

  <!-- 箭头指向中间模块 -->
  <line class="arrow" x1="320" y1="480" x2="360" y2="480" marker-end="url(#arrowHead)"/>

  <!-- 中间三个模块（堆叠） -->
  <rect class="solidBox" x="360" y="455" width="90" height="25"/>
  <text class="smallText" x="405" y="472">实体解析</text>

  <rect class="solidBox" x="360" y="485" width="90" height="25"/>
  <text class="smallText" x="405" y="502">时间对齐</text>

  <rect class="solidBox" x="360" y="515" width="90" height="25"/>
  <text class="smallText" x="405" y="532">证据链生成</text>

  <rect class="solidBox" x="455" y="485" width="90" height="25"/>
  <text class="smallText" x="500" y="502">LLM 解释模板</text>

  <rect class="solidBox" x="550" y="515" width="110" height="25"/>
  <text class="smallText" x="605" y="532">分流与优先级（风险—代价）</text>

  <!-- 箭头指向右侧编排与治理 -->
  <line class="arrow" x1="660" y1="480" x2="700" y2="480" marker-end="url(#arrowHead)"/>

  <!-- 右侧：编排联动与治理闸口 -->
  <rect class="solidBox" x="700" y="460" width="110" height="40"/>
  <text class="smallText" x="755" y="475">编排联动（SOAR）</text>
  <text class="smallText" x="755" y="490">人审/回退/留痕/通报</text>

  <!-- 箭头从第四层到第五层 -->
  <line class="arrow" x1="400" y1="570" x2="400" y2="600" marker-end="url(#arrowHead)"/>

  <!-- 第五层：流程级评估与验证 -->
  <rect class="dashedBox" x="50" y="600" width="700" height="120"/>
  <text class="sectionTitle" x="400" y="625">流程级评估与验证</text>

  <!-- 左至右评估流程 -->
  <rect class="solidBox" x="80" y="650" width="100" height="30"/>
  <text class="smallText" x="130" y="665">评估设计</text>

  <line class="arrow" x1="180" y1="665" x2="220" y2="665" marker-end="url(#arrowHead)"/>

  <rect class="solidBox" x="220" y="650" width="130" height="30"/>
  <text class="smallText" x="285" y="665">对照设置（回放/准实验）</text>

  <line class="arrow" x1="350" y1="665" x2="390" y2="665" marker-end="url(#arrowHead)"/>

  <rect class="solidBox" x="390" y="650" width="120" height="30"/>
  <text class="smallText" x="450" y="665">指标计算与统计</text>

  <line class="arrow" x1="510" y1="665" x2="550" y2="665" marker-end="url(#arrowHead)"/>

  <rect class="solidBox" x="550" y="650" width="120" height="30"/>
  <text class="smallText" x="610" y="665">过程挖掘与消融</text>

  <line class="arrow" x1="670" y1="665" x2="710" y2="665" marker-end="url(#arrowHead)"/>

  <rect class="solidBox" x="710" y="650" width="100" height="30"/>
  <text class="smallText" x="760" y="665">结果分析与结论</text>

  <!-- 箭头从第五层到第六层 -->
  <line class="arrow" x1="400" y1="720" x2="400" y2="750" marker-end="url(#arrowHead)"/>

  <!-- 第六层：总结与固化 -->
  <rect class="dashedBox" x="50" y="750" width="700" height="80"/>
  <text class="sectionTitle" x="400" y="775">总结与固化</text>

  <!-- 两个子模块 -->
  <rect class="solidBox" x="200" y="795" width="160" height="25"/>
  <text class="boxText" x="280" y="812">设计原则与评价清单 / 适用边界</text>

  <rect class="solidBox" x="400" y="795" width="160" height="25"/>
  <text class="boxText" x="480" y="812">策略/剧本与知识回写 / 研究展望</text>

  <!-- 右侧方法论模块 -->
  <g>
    <!-- 研究方法（文献+访谈） -->
    <rect class="methodBox" x="800" y="50" width="140" height="50"/>
    <text class="smallText" x="870" y="70">研究方法</text>
    <text class="smallText" x="870" y="86">文献调研 / 访谈</text>

    <!-- 理论与范式（BPR/DSR） -->
    <rect class="methodBox" x="800" y="160" width="140" height="40"/>
    <text class="smallText" x="870" y="175">BPR / DSR</text>

    <!-- 知识工程（OCSF/ATT&CK） -->
    <rect class="methodBox" x="800" y="270" width="140" height="40"/>
    <text class="smallText" x="870" y="285">OCSF / ATT&amp;CK</text>

    <!-- 设计与实现（原型迭代） -->
    <rect class="methodBox" x="800" y="420" width="140" height="40"/>
    <text class="smallText" x="870" y="435">DSR 原型迭代</text>

    <!-- 评估方法（准实验/统计/挖掘/消融） -->
    <rect class="methodBox" x="800" y="600" width="140" height="50"/>
    <text class="smallText" x="870" y="618">准实验 / 统计检验</text>
    <text class="smallText" x="870" y="634">过程挖掘 / 消融</text>

    <!-- 合规与伦理（治理约束） -->
    <rect class="methodBox" x="800" y="750" width="140" height="40"/>
    <text class="smallText" x="870" y="765">合规与伦理约束</text>
  </g>

</svg>