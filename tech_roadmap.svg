<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <style>
      .lane-title { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; fill: #2C3E50; }
      .column-title { font-family: Arial, sans-serif; font-size: 11px; font-weight: bold; fill: #2C3E50; text-anchor: middle; }
      .node-text { font-family: Arial, sans-serif; font-size: 9px; fill: #2C3E50; text-anchor: middle; }
      .gov-text { font-family: Arial, sans-serif; font-size: 10px; fill: #8B4513; text-anchor: middle; font-weight: bold; }
      .milestone { fill: #E74C3C; stroke: #C0392B; stroke-width: 2; }
      .arrow { stroke: #5D6D7E; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #5D6D7E; stroke-width: 2; fill: none; stroke-dasharray: 6,4; marker-end: url(#arrowhead); }
    </style>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#5D6D7E" />
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="1200" height="800" fill="#FFFFFF" stroke="#E5E5E5" stroke-width="1"/>

  <!-- 顶部治理横幅 -->
  <rect x="40" y="50" width="1120" height="60" fill="#F9E79F" stroke="#F4D03F" stroke-width="1" rx="5"/>
  <text x="600" y="75" class="gov-text">治理与合规约束</text>
  <text x="600" y="95" class="gov-text" font-size="8">数据最小化与脱敏 | 角色授权与分权 | 高风险动作人审与回退 | 全量留痕与审计 | 重大事件通报 | 高风险误处置=0</text>

  <!-- 列标题 -->
  <text x="130" y="135" class="column-title">A 问题识别与流程诊断</text>
  <text x="320" y="135" class="column-title">B 数据准备与语义统一</text>
  <text x="510" y="135" class="column-title">C 协同构件设计与原型V1</text>
  <text x="700" y="135" class="column-title">D 形成性评估与迭代(P1)</text>
  <text x="890" y="135" class="column-title">E 流程级评估与准实验(P2)</text>
  <text x="1080" y="135" class="column-title">F 闭环固化与知识回写</text>

  <!-- 泳道1: 数据与语义 -->
  <rect x="40" y="150" width="1120" height="100" fill="#D6EAF8" stroke="#AED6F1" stroke-width="1" rx="3"/>
  <text x="10" y="205" class="lane-title" transform="rotate(-90, 10, 205)">数据与语义</text>
  
  <!-- 泳道2: 知识与标准 -->
  <rect x="40" y="262" width="1120" height="100" fill="#E8F6F3" stroke="#A3E4D7" stroke-width="1" rx="3"/>
  <text x="10" y="317" class="lane-title" transform="rotate(-90, 10, 317)">知识与标准</text>
  
  <!-- 泳道3: 上下文与证据链 -->
  <rect x="40" y="374" width="1120" height="100" fill="#FEF5E7" stroke="#F8C471" stroke-width="1" rx="3"/>
  <text x="10" y="429" class="lane-title" transform="rotate(-90, 10, 429)">上下文与证据链</text>
  
  <!-- 泳道4: 分流与优先级 -->
  <rect x="40" y="486" width="1120" height="100" fill="#FDEDEC" stroke="#F1948A" stroke-width="1" rx="3"/>
  <text x="10" y="541" class="lane-title" transform="rotate(-90, 10, 541)">分流与优先级</text>
  
  <!-- 泳道5: 编排与治理闸口 -->
  <rect x="40" y="598" width="1120" height="100" fill="#E8DAEF" stroke="#D2B4DE" stroke-width="1" rx="3"/>
  <text x="10" y="653" class="lane-title" transform="rotate(-90, 10, 653)">编排与治理闸口</text>
  
  <!-- 泳道6: 度量与过程挖掘 -->
  <rect x="40" y="710" width="1120" height="100" fill="#EBEDEF" stroke="#D5DBDB" stroke-width="1" rx="3"/>
  <text x="10" y="765" class="lane-title" transform="rotate(-90, 10, 765)">度量与过程挖掘</text>

  <!-- 节点内容 - 第A列 -->
  <rect x="50" y="165" width="160" height="70" fill="#FFFFFF" stroke="#85C1E9" stroke-width="1" rx="3"/>
  <text x="130" y="185" class="node-text">访谈与日志盘点</text>
  <text x="130" y="200" class="node-text">字段清单与数据沿袭</text>

  <rect x="50" y="277" width="160" height="70" fill="#FFFFFF" stroke="#7FB3D3" stroke-width="1" rx="3"/>
  <text x="130" y="297" class="node-text">知识盘点与口径</text>
  <text x="130" y="312" class="node-text">对齐项</text>

  <rect x="50" y="389" width="160" height="70" fill="#FFFFFF" stroke="#F7DC6F" stroke-width="1" rx="3"/>
  <text x="130" y="409" class="node-text">关键实体与</text>
  <text x="130" y="424" class="node-text">会话定义</text>

  <rect x="50" y="501" width="160" height="70" fill="#FFFFFF" stroke="#F1948A" stroke-width="1" rx="3"/>
  <text x="130" y="521" class="node-text">业务关键度与资产</text>
  <text x="130" y="536" class="node-text">重要度建模需求</text>

  <rect x="50" y="613" width="160" height="70" fill="#FFFFFF" stroke="#D2B4DE" stroke-width="1" rx="3"/>
  <text x="130" y="633" class="node-text">现有SOAR/工单接口清单</text>
  <text x="130" y="648" class="node-text">治理边界定义</text>

  <rect x="50" y="725" width="160" height="70" fill="#FFFFFF" stroke="#D5DBDB" stroke-width="1" rx="3"/>
  <text x="130" y="745" class="node-text">指标体系与事件日志</text>
  <text x="130" y="760" class="node-text">口径设计</text>

  <!-- 节点内容 - 第B列 -->
  <rect x="240" y="165" width="160" height="70" fill="#FFFFFF" stroke="#85C1E9" stroke-width="1" rx="3"/>
  <text x="320" y="185" class="node-text">OCSF映射样例</text>
  <text x="320" y="200" class="node-text">脱敏清洗与字段对齐</text>
  <text x="320" y="215" class="node-text">实体解析与时间对齐基线</text>

  <rect x="240" y="277" width="160" height="70" fill="#FFFFFF" stroke="#7FB3D3" stroke-width="1" rx="3"/>
  <text x="320" y="297" class="node-text">ATT&CK映射模板</text>
  <text x="320" y="312" class="node-text">SOP/策略与案例结构化</text>

  <rect x="240" y="389" width="160" height="70" fill="#FFFFFF" stroke="#F7DC6F" stroke-width="1" rx="3"/>
  <text x="320" y="409" class="node-text">跨源时间线生成</text>
  <text x="320" y="424" class="node-text">证据清单结构</text>

  <rect x="240" y="501" width="160" height="70" fill="#FFFFFF" stroke="#F1948A" stroke-width="1" rx="3"/>
  <text x="320" y="521" class="node-text">风险-代价参数初设</text>
  <text x="320" y="536" class="node-text">近重复合并/相似场景聚类</text>

  <rect x="240" y="613" width="160" height="70" fill="#FFFFFF" stroke="#D2B4DE" stroke-width="1" rx="3"/>
  <text x="320" y="633" class="node-text">动作模板定义</text>
  <text x="320" y="648" class="node-text">(隔离/封禁/阻断/情报查询)</text>

  <rect x="240" y="725" width="160" height="70" fill="#FFFFFF" stroke="#D5DBDB" stroke-width="1" rx="3"/>
  <text x="320" y="745" class="node-text">埋点落地</text>
  <text x="320" y="760" class="node-text">采集验证</text>

  <!-- 节点内容 - 第C列 -->
  <rect x="430" y="165" width="160" height="70" fill="#FFFFFF" stroke="#85C1E9" stroke-width="1" rx="3"/>
  <text x="510" y="185" class="node-text">事件标准化流水线</text>
  <text x="510" y="200" class="node-text">(批/流)稳定性校验</text>

  <rect x="430" y="277" width="160" height="70" fill="#FFFFFF" stroke="#7FB3D3" stroke-width="1" rx="3"/>
  <text x="510" y="297" class="node-text">知识索引与检索</text>
  <text x="510" y="312" class="node-text">元数据与版本管理</text>

  <rect x="430" y="389" width="160" height="70" fill="#FFFFFF" stroke="#F7DC6F" stroke-width="1" rx="3"/>
  <text x="510" y="409" class="node-text">时间线—证据—结论生成</text>
  <text x="510" y="424" class="node-text">LLM用于语言化解释</text>

  <rect x="430" y="501" width="160" height="70" fill="#FFFFFF" stroke="#F1948A" stroke-width="1" rx="3"/>
  <text x="510" y="521" class="node-text">分流与优先级建议</text>
  <text x="510" y="536" class="node-text">(附证据与阈值理由)</text>

  <rect x="430" y="613" width="160" height="70" fill="#FFFFFF" stroke="#D2B4DE" stroke-width="1" rx="3"/>
  <text x="510" y="633" class="node-text">接口联调;人审闸口</text>
  <text x="510" y="648" class="node-text">回退路径;全量留痕</text>

  <rect x="430" y="725" width="160" height="70" fill="#FFFFFF" stroke="#D5DBDB" stroke-width="1" rx="3"/>
  <text x="510" y="745" class="node-text">指标看板原型</text>
  <text x="510" y="760" class="node-text">日志抽取脚本</text>

  <!-- 节点内容 - 第D列 -->
  <rect x="620" y="165" width="160" height="70" fill="#FFFFFF" stroke="#85C1E9" stroke-width="1" rx="3"/>
  <text x="700" y="185" class="node-text">异常/漂移样例收集</text>
  <text x="700" y="200" class="node-text">规则与阈值微调</text>

  <rect x="620" y="277" width="160" height="70" fill="#FFFFFF" stroke="#7FB3D3" stroke-width="1" rx="3"/>
  <text x="700" y="297" class="node-text">解释模板试用反馈</text>
  <text x="700" y="312" class="node-text">口径一致性审查</text>

  <rect x="620" y="389" width="160" height="70" fill="#FFFFFF" stroke="#F7DC6F" stroke-width="1" rx="3"/>
  <text x="700" y="409" class="node-text">解释一致性优化</text>
  <text x="700" y="424" class="node-text">模板迭代</text>

  <rect x="620" y="501" width="160" height="70" fill="#FFFFFF" stroke="#F1948A" stroke-width="1" rx="3"/>
  <text x="700" y="521" class="node-text">阈值/权重调优</text>
  <text x="700" y="536" class="node-text">人工审阅规则</text>

  <rect x="620" y="613" width="160" height="70" fill="#FFFFFF" stroke="#D2B4DE" stroke-width="1" rx="3"/>
  <text x="700" y="633" class="node-text">闸口策略细化</text>
  <text x="700" y="648" class="node-text">敏感操作双人复核</text>

  <rect x="620" y="725" width="160" height="70" fill="#FFFFFF" stroke="#D5DBDB" stroke-width="1" rx="3"/>
  <text x="700" y="745" class="node-text">形成性评估报告</text>
  <text x="700" y="760" class="node-text">指标与脚本优化</text>
  <!-- P1里程碑 -->
  <circle cx="770" cy="735" r="8" class="milestone"/>
  <text x="770" y="740" class="node-text" fill="#FFFFFF" font-weight="bold">P1</text>

  <!-- 节点内容 - 第E列 -->
  <rect x="810" y="165" width="160" height="70" fill="#FFFFFF" stroke="#85C1E9" stroke-width="1" rx="3"/>
  <text x="890" y="185" class="node-text">评估口径一致化</text>
  <text x="890" y="200" class="node-text">离线回放数据集</text>

  <rect x="810" y="277" width="160" height="70" fill="#FFFFFF" stroke="#7FB3D3" stroke-width="1" rx="3"/>
  <text x="890" y="297" class="node-text">证据-知识对齐质量评估</text>
  <text x="890" y="312" class="node-text">(覆盖度、一致性)</text>

  <rect x="810" y="389" width="160" height="70" fill="#FFFFFF" stroke="#F7DC6F" stroke-width="1" rx="3"/>
  <text x="890" y="409" class="node-text">证据链完整性评估</text>
  <text x="890" y="424" class="node-text">(覆盖度/条目数/一致性)</text>

  <rect x="810" y="501" width="160" height="70" fill="#FFFFFF" stroke="#F1948A" stroke-width="1" rx="3"/>
  <text x="890" y="521" class="node-text">FPR、合并/去重</text>
  <text x="890" y="536" class="node-text">一次性完成率评估</text>

  <rect x="810" y="613" width="160" height="70" fill="#FFFFFF" stroke="#D2B4DE" stroke-width="1" rx="3"/>
  <text x="890" y="633" class="node-text">自动化覆盖/成功率</text>
  <text x="890" y="648" class="node-text">升级/回退比例评估</text>

  <rect x="810" y="725" width="160" height="70" fill="#FFFFFF" stroke="#D5DBDB" stroke-width="1" rx="3"/>
  <text x="890" y="745" class="node-text">统计分析(相对改变量/效应量)</text>
  <text x="890" y="760" class="node-text">流程图谱前后对比;消融实验</text>
  <!-- P2里程碑 -->
  <circle cx="960" cy="735" r="8" class="milestone"/>
  <text x="960" y="740" class="node-text" fill="#FFFFFF" font-weight="bold">P2</text>

  <!-- 节点内容 - 第F列 -->
  <rect x="1000" y="165" width="160" height="70" fill="#FFFFFF" stroke="#85C1E9" stroke-width="1" rx="3"/>
  <text x="1080" y="185" class="node-text">数据字典与处理</text>
  <text x="1080" y="200" class="node-text">规范固化</text>

  <rect x="1000" y="277" width="160" height="70" fill="#FFFFFF" stroke="#7FB3D3" stroke-width="1" rx="3"/>
  <text x="1080" y="297" class="node-text">有效策略回写</text>
  <text x="1080" y="312" class="node-text">知识更新节拍</text>

  <rect x="1000" y="389" width="160" height="70" fill="#FFFFFF" stroke="#F7DC6F" stroke-width="1" rx="3"/>
  <text x="1080" y="409" class="node-text">最佳实践模板</text>
  <text x="1080" y="424" class="node-text">沉淀</text>

  <rect x="1000" y="501" width="160" height="70" fill="#FFFFFF" stroke="#F1948A" stroke-width="1" rx="3"/>
  <text x="1080" y="521" class="node-text">策略参数与</text>
  <text x="1080" y="536" class="node-text">阈值固化</text>

  <rect x="1000" y="613" width="160" height="70" fill="#FFFFFF" stroke="#D2B4DE" stroke-width="1" rx="3"/>
  <text x="1080" y="633" class="node-text">剧本与闸口策略固化</text>
  <text x="1080" y="648" class="node-text">合规文档完善</text>

  <rect x="1000" y="725" width="160" height="70" fill="#FFFFFF" stroke="#D5DBDB" stroke-width="1" rx="3"/>
  <text x="1080" y="745" class="node-text">评估归档与复现实验包</text>
  <text x="1080" y="760" class="node-text">持续改进节拍</text>

  <!-- 水平主干箭头 (阶段推进) -->
  <!-- 第1行 -->
  <line x1="210" y1="200" x2="230" y2="200" class="arrow"/>
  <line x1="400" y1="200" x2="420" y2="200" class="arrow"/>
  <line x1="590" y1="200" x2="610" y2="200" class="arrow"/>
  <line x1="780" y1="200" x2="800" y2="200" class="arrow"/>
  <line x1="970" y1="200" x2="990" y2="200" class="arrow"/>

  <!-- 第2行 -->
  <line x1="210" y1="312" x2="230" y2="312" class="arrow"/>
  <line x1="400" y1="312" x2="420" y2="312" class="arrow"/>
  <line x1="590" y1="312" x2="610" y2="312" class="arrow"/>
  <line x1="780" y1="312" x2="800" y2="312" class="arrow"/>
  <line x1="970" y1="312" x2="990" y2="312" class="arrow"/>

  <!-- 第3行 -->
  <line x1="210" y1="424" x2="230" y2="424" class="arrow"/>
  <line x1="400" y1="424" x2="420" y2="424" class="arrow"/>
  <line x1="590" y1="424" x2="610" y2="424" class="arrow"/>
  <line x1="780" y1="424" x2="800" y2="424" class="arrow"/>
  <line x1="970" y1="424" x2="990" y2="424" class="arrow"/>

  <!-- 第4行 -->
  <line x1="210" y1="536" x2="230" y2="536" class="arrow"/>
  <line x1="400" y1="536" x2="420" y2="536" class="arrow"/>
  <line x1="590" y1="536" x2="610" y2="536" class="arrow"/>
  <line x1="780" y1="536" x2="800" y2="536" class="arrow"/>
  <line x1="970" y1="536" x2="990" y2="536" class="arrow"/>

  <!-- 第5行 -->
  <line x1="210" y1="648" x2="230" y2="648" class="arrow"/>
  <line x1="400" y1="648" x2="420" y2="648" class="arrow"/>
  <line x1="590" y1="648" x2="610" y2="648" class="arrow"/>
  <line x1="780" y1="648" x2="800" y2="648" class="arrow"/>
  <line x1="970" y1="648" x2="990" y2="648" class="arrow"/>

  <!-- 第6行 -->
  <line x1="210" y1="760" x2="230" y2="760" class="arrow"/>
  <line x1="400" y1="760" x2="420" y2="760" class="arrow"/>
  <line x1="590" y1="760" x2="610" y2="760" class="arrow"/>
  <line x1="780" y1="760" x2="800" y2="760" class="arrow"/>
  <line x1="970" y1="760" x2="990" y2="760" class="arrow"/>

  <!-- 垂直依赖箭头 -->
  <!-- B1 → B3 (OCSF → 时间线) -->
  <line x1="320" y1="235" x2="320" y2="380" class="arrow"/>
  <text x="325" y="310" class="node-text" font-size="8">统一事件/实体</text>

  <!-- B3 → C3 (时间线 → 证据链) -->
  <line x1="400" y1="424" x2="420" y2="424" class="arrow"/>

  <!-- C3 → C4 (证据链 → 分流) -->
  <line x1="510" y1="459" x2="510" y2="491" class="arrow"/>
  <text x="515" y="480" class="node-text" font-size="8">证据→分流</text>

  <!-- C4 → C5 (分流 → 编排) -->
  <line x1="510" y1="571" x2="510" y2="603" class="arrow"/>
  <text x="515" y="590" class="node-text" font-size="8">建议→动作</text>

  <!-- 治理约束虚线 -->
  <line x1="600" y1="110" x2="510" y2="603" class="dashed-arrow"/>
  <text x="520" y="350" class="node-text" font-size="8">治理约束</text>

  <!-- B2 → C3 (ATT&CK → 证据链) -->
  <line x1="400" y1="312" x2="430" y2="409" class="arrow"/>

  <!-- A6 → B6 → E6 (指标设计 → 埋点 → 统计挖掘) -->
  <line x1="210" y1="760" x2="230" y2="760" class="arrow"/>
  <line x1="780" y1="760" x2="800" y2="760" class="arrow"/>

  <!-- 图例 -->
  <rect x="950" y="650" width="240" height="120" fill="#FFFFFF" stroke="#BDC3C7" stroke-width="1" rx="5"/>
  <text x="1070" y="670" class="lane-title" text-anchor="middle">图例</text>

  <!-- 颜色图例 -->
  <rect x="960" y="680" width="15" height="10" fill="#D6EAF8"/>
  <text x="980" y="690" class="node-text" font-size="8">数据与语义</text>

  <rect x="960" y="695" width="15" height="10" fill="#E8F6F3"/>
  <text x="980" y="705" class="node-text" font-size="8">知识与标准</text>

  <rect x="960" y="710" width="15" height="10" fill="#FEF5E7"/>
  <text x="980" y="720" class="node-text" font-size="8">上下文与证据链</text>

  <rect x="960" y="725" width="15" height="10" fill="#FDEDEC"/>
  <text x="980" y="735" class="node-text" font-size="8">分流与优先级</text>

  <rect x="1100" y="680" width="15" height="10" fill="#E8DAEF"/>
  <text x="1120" y="690" class="node-text" font-size="8">编排与治理闸口</text>

  <rect x="1100" y="695" width="15" height="10" fill="#EBEDEF"/>
  <text x="1120" y="705" class="node-text" font-size="8">度量与过程挖掘</text>

  <rect x="1100" y="710" width="15" height="10" fill="#F9E79F"/>
  <text x="1120" y="720" class="node-text" font-size="8">治理与合规(贯穿)</text>

  <!-- 箭头图例 -->
  <line x1="960" y1="745" x2="980" y2="745" stroke="#5D6D7E" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="985" y="750" class="node-text" font-size="8">数据/控制流</text>

  <line x1="1100" y1="745" x2="1120" y2="745" stroke="#5D6D7E" stroke-width="2" stroke-dasharray="6,4" marker-end="url(#arrowhead)"/>
  <text x="1125" y="750" class="node-text" font-size="8">治理约束</text>

  <!-- 里程碑图例 -->
  <circle cx="970" cy="760" r="5" class="milestone"/>
  <text x="980" y="765" class="node-text" font-size="8">P1/P2里程碑</text>

</svg>
