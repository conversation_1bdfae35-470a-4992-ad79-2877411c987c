{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "text", "version": 1, "versionNonce": 101, "isDeleted": false, "id": "title", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 100, "y": 40, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 520, "height": 28, "seed": 101, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "Focus on identity and systems（流程图）", "fontSize": 24, "fontFamily": 1, "textAlign": "left", "verticalAlign": "top", "baseline": 22, "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 201, "isDeleted": false, "id": "node-identity", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 100, "y": 100, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 160, "height": 60, "seed": 201, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 202, "isDeleted": false, "id": "text-identity", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 120, "y": 118, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 120, "height": 24, "seed": 202, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "Identity（身份）", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18, "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 203, "isDeleted": false, "id": "node-systems", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 300, "y": 100, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 160, "height": 60, "seed": 203, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 204, "isDeleted": false, "id": "text-systems", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 320, "y": 118, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 120, "height": 24, "seed": 204, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "Systems（系统）", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18, "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 205, "isDeleted": false, "id": "node-habits", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 500, "y": 100, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 160, "height": 60, "seed": 205, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 206, "isDeleted": false, "id": "text-habits", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 520, "y": 118, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 120, "height": 24, "seed": 206, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "Habits（习惯）", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18, "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 207, "isDeleted": false, "id": "node-outcomes", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 700, "y": 100, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 160, "height": 60, "seed": 207, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 208, "isDeleted": false, "id": "text-outcomes", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 720, "y": 118, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 120, "height": 24, "seed": 208, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "Outcomes（结果）", "fontSize": 20, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 18, "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 301, "isDeleted": false, "id": "edge-1", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 260, "y": 130, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 40, "height": 0, "seed": 301, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [40, 0]]}, {"type": "arrow", "version": 1, "versionNonce": 302, "isDeleted": false, "id": "edge-2", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 460, "y": 130, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 40, "height": 0, "seed": 302, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [40, 0]]}, {"type": "arrow", "version": 1, "versionNonce": 303, "isDeleted": false, "id": "edge-3", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 660, "y": 130, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 40, "height": 0, "seed": 303, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [40, 0]]}, {"type": "rectangle", "version": 1, "versionNonce": 401, "isDeleted": false, "id": "note-identity-change", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 100, "y": 180, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 200, "height": 48, "seed": 401, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 402, "isDeleted": false, "id": "text-identity-change", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 110, "y": 194, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 180, "height": 22, "seed": 402, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "行为改变 = 身份改变", "fontSize": 16, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 16, "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 407, "isDeleted": false, "id": "note-systems-over-goals", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 300, "y": 180, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 200, "height": 48, "seed": 407, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 408, "isDeleted": false, "id": "text-systems-over-goals", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 310, "y": 194, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 180, "height": 22, "seed": 408, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "关注系统而非目标", "fontSize": 16, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 16, "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 413, "isDeleted": false, "id": "note-habits-lead", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 500, "y": 180, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 200, "height": 48, "seed": 413, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 414, "isDeleted": false, "id": "text-habits-lead", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 510, "y": 194, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 180, "height": 22, "seed": 414, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "我们随习惯而行", "fontSize": 16, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 16, "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 501, "isDeleted": false, "id": "stage-cue", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 240, "y": 300, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 150, "height": 50, "seed": 501, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 502, "isDeleted": false, "id": "text-cue", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 260, "y": 316, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 110, "height": 22, "seed": 502, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "1. <PERSON><PERSON> 线索", "fontSize": 16, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 16, "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 503, "isDeleted": false, "id": "stage-craving", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 440, "y": 300, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 150, "height": 50, "seed": 503, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 504, "isDeleted": false, "id": "text-craving", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 460, "y": 316, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 110, "height": 22, "seed": 504, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "2. Craving 渴望", "fontSize": 16, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 16, "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 505, "isDeleted": false, "id": "stage-response", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 640, "y": 300, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 150, "height": 50, "seed": 505, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 506, "isDeleted": false, "id": "text-response", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 660, "y": 316, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 110, "height": 22, "seed": 506, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "3. Response 反应", "fontSize": 16, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 16, "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 507, "isDeleted": false, "id": "stage-reward", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 840, "y": 300, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 150, "height": 50, "seed": 507, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 508, "isDeleted": false, "id": "text-reward", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 860, "y": 316, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 110, "height": 22, "seed": 508, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "text": "4. <PERSON><PERSON> 奖赏", "fontSize": 16, "fontFamily": 1, "textAlign": "center", "verticalAlign": "middle", "baseline": 16, "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 509, "isDeleted": false, "id": "edge-cue-craving", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 390, "y": 325, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 50, "height": 0, "seed": 509, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [50, 0]]}, {"type": "arrow", "version": 1, "versionNonce": 510, "isDeleted": false, "id": "edge-craving-response", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 590, "y": 325, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 50, "height": 0, "seed": 510, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [50, 0]]}, {"type": "arrow", "version": 1, "versionNonce": 511, "isDeleted": false, "id": "edge-response-reward", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 790, "y": 325, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 50, "height": 0, "seed": 511, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [50, 0]]}, {"type": "arrow", "version": 1, "versionNonce": 512, "isDeleted": false, "id": "edge-reward-cue", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 840, "y": 300, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": -540, "height": -80, "seed": 512, "groupIds": [], "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": null, "endBinding": null, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [-270, -80], [-540, 0]]}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}