{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "text", "version": 1, "versionNonce": 101, "isDeleted": false, "id": "title", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 80, "y": -60, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 520, "height": 28, "seed": 101, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1, "fontSize": 24, "fontFamily": 1, "text": "Focus on identity and systems（基于图片的流程提炼）", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Focus on identity and systems（基于图片的流程提炼）", "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 201, "isDeleted": false, "id": "node-identity", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 80, "y": 10, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 180, "height": 56, "seed": 201, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-identity"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 202, "isDeleted": false, "id": "text-identity", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 100, "y": 26, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 140, "height": 24, "seed": 202, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1, "fontSize": 20, "fontFamily": 1, "text": "Identity（身份）", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-identity", "originalText": "Identity（身份）", "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 203, "isDeleted": false, "id": "node-systems", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 320, "y": 10, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 200, "height": 56, "seed": 203, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-systems"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 204, "isDeleted": false, "id": "text-systems", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 340, "y": 26, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 160, "height": 24, "seed": 204, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1, "fontSize": 20, "fontFamily": 1, "text": "Systems（系统/过程）", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-systems", "originalText": "Systems（系统/过程）", "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 205, "isDeleted": false, "id": "node-habits", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 580, "y": 10, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 180, "height": 56, "seed": 205, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-habits"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 206, "isDeleted": false, "id": "text-habits", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 600, "y": 26, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 140, "height": 24, "seed": 206, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1, "fontSize": 20, "fontFamily": 1, "text": "Habits（习惯）", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-habits", "originalText": "Habits（习惯）", "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 207, "isDeleted": false, "id": "node-outcomes", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 820, "y": 10, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 180, "height": 56, "seed": 207, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-outcomes"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 208, "isDeleted": false, "id": "text-outcomes", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 840, "y": 26, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 140, "height": 24, "seed": 208, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1, "fontSize": 20, "fontFamily": 1, "text": "Outcomes（结果）", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-outcomes", "originalText": "Outcomes（结果）", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 301, "isDeleted": false, "id": "edge-identity-systems", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 260, "y": 38, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 60, "height": 0, "seed": 301, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [60, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-identity", "focus": 0, "gap": 0}, "endBinding": {"elementId": "node-systems", "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "arrow", "version": 1, "versionNonce": 302, "isDeleted": false, "id": "edge-systems-habits", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 520, "y": 38, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 60, "height": 0, "seed": 302, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [60, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-systems", "focus": 0, "gap": 0}, "endBinding": {"elementId": "node-habits", "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "arrow", "version": 1, "versionNonce": 303, "isDeleted": false, "id": "edge-habits-outcomes", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 760, "y": 38, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 60, "height": 0, "seed": 303, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [60, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-habits", "focus": 0, "gap": 0}, "endBinding": {"elementId": "node-outcomes", "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 401, "isDeleted": false, "id": "node-identity-change", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 60, "y": 90, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 220, "height": 56, "seed": 401, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-identity-change"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 402, "isDeleted": false, "id": "text-identity-change", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 80, "y": 106, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 180, "height": 24, "seed": 402, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1, "fontSize": 18, "fontFamily": 1, "text": "行为改变=身份改变", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-identity-change", "originalText": "行为改变=身份改变", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 403, "isDeleted": false, "id": "edge-identity-to-identitychange", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 160, "y": 66, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 24, "seed": 403, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, 24]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-identity", "focus": 0, "gap": 0}, "endBinding": {"elementId": "node-identity-change", "focus": 0, "gap": 8}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 404, "isDeleted": false, "id": "node-express-identity", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 60, "y": 160, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 220, "height": 56, "seed": 404, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-express-identity"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 405, "isDeleted": false, "id": "text-express-identity", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 80, "y": 176, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 180, "height": 24, "seed": 405, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1, "fontSize": 18, "fontFamily": 1, "text": "表达身份（自我叙述）", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-express-identity", "originalText": "表达身份（自我叙述）", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 406, "isDeleted": false, "id": "edge-identity-express", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 160, "y": 146, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 14, "seed": 406, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, 14]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-identity-change", "focus": 0, "gap": 6}, "endBinding": {"elementId": "node-express-identity", "focus": 0, "gap": 8}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 407, "isDeleted": false, "id": "node-systems-over-goals", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 320, "y": 90, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 200, "height": 56, "seed": 407, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-systems-over-goals"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 408, "isDeleted": false, "id": "text-systems-over-goals", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 340, "y": 106, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 160, "height": 24, "seed": 408, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1, "fontSize": 18, "fontFamily": 1, "text": "关注系统而非目标", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-systems-over-goals", "originalText": "关注系统而非目标", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 409, "isDeleted": false, "id": "edge-systems-branch", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 420, "y": 66, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 24, "seed": 409, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, 24]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-systems", "focus": 0, "gap": 0}, "endBinding": {"elementId": "node-systems-over-goals", "focus": 0, "gap": 8}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 410, "isDeleted": false, "id": "node-strengths", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 320, "y": 160, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 200, "height": 56, "seed": 410, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-strengths"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 411, "isDeleted": false, "id": "text-strengths", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 340, "y": 176, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 160, "height": 24, "seed": 411, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1, "fontSize": 18, "fontFamily": 1, "text": "按优势构建习惯", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-strengths", "originalText": "按优势构建习惯", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 412, "isDeleted": false, "id": "edge-systems-strengths", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 420, "y": 146, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 14, "seed": 412, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, 14]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-systems-over-goals", "focus": 0, "gap": 6}, "endBinding": {"elementId": "node-strengths", "focus": 0, "gap": 8}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 413, "isDeleted": false, "id": "node-habits-lead", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 560, "y": 90, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 220, "height": 56, "seed": 413, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-habits-lead"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 414, "isDeleted": false, "id": "text-habits-lead", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 580, "y": 106, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 180, "height": 24, "seed": 414, "groupIds": [], "roundness": null, "boundElements": [], "updated": 1, "fontSize": 18, "fontFamily": 1, "text": "我们随习惯而行", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-habits-lead", "originalText": "我们随习惯而行", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 415, "isDeleted": false, "id": "edge-habits-branch", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 670, "y": 66, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 24, "seed": 415, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, 24]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-habits", "focus": 0, "gap": 0}, "endBinding": {"elementId": "node-habits-lead", "focus": 0, "gap": 8}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 501, "isDeleted": false, "id": "stage-cue", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 220, "y": 270, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 150, "height": 48, "seed": 501, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-cue"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 502, "isDeleted": false, "id": "text-cue", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 240, "y": 284, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 110, "height": 24, "seed": 502, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 18, "fontFamily": 1, "text": "1. <PERSON><PERSON> 线索", "textAlign": "center", "verticalAlign": "middle", "containerId": "stage-cue", "originalText": "1. <PERSON><PERSON> 线索", "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 503, "isDeleted": false, "id": "stage-craving", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 420, "y": 270, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 150, "height": 48, "seed": 503, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-craving"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 504, "isDeleted": false, "id": "text-craving", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 440, "y": 284, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 110, "height": 24, "seed": 504, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 18, "fontFamily": 1, "text": "2. Craving 渴望", "textAlign": "center", "verticalAlign": "middle", "containerId": "stage-craving", "originalText": "2. Craving 渴望", "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 505, "isDeleted": false, "id": "stage-response", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 620, "y": 270, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 150, "height": 48, "seed": 505, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-response"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 506, "isDeleted": false, "id": "text-response", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 640, "y": 284, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 110, "height": 24, "seed": 506, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 18, "fontFamily": 1, "text": "3. Response 反应", "textAlign": "center", "verticalAlign": "middle", "containerId": "stage-response", "originalText": "3. Response 反应", "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 507, "isDeleted": false, "id": "stage-reward", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 820, "y": 270, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 150, "height": 48, "seed": 507, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-reward"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 508, "isDeleted": false, "id": "text-reward", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 840, "y": 284, "strokeColor": "#000000", "backgroundColor": "transparent", "width": 110, "height": 24, "seed": 508, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 18, "fontFamily": 1, "text": "4. <PERSON><PERSON> 奖赏", "textAlign": "center", "verticalAlign": "middle", "containerId": "stage-reward", "originalText": "4. <PERSON><PERSON> 奖赏", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 509, "isDeleted": false, "id": "edge-cue-craving", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 370, "y": 294, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 50, "height": 0, "seed": 509, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [50, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "stage-cue", "focus": 0, "gap": 0}, "endBinding": {"elementId": "stage-craving", "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "arrow", "version": 1, "versionNonce": 510, "isDeleted": false, "id": "edge-craving-response", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 570, "y": 294, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 50, "height": 0, "seed": 510, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [50, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "stage-craving", "focus": 0, "gap": 0}, "endBinding": {"elementId": "stage-response", "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "arrow", "version": 1, "versionNonce": 511, "isDeleted": false, "id": "edge-response-reward", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 770, "y": 294, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 50, "height": 0, "seed": 511, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [50, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "stage-response", "focus": 0, "gap": 0}, "endBinding": {"elementId": "stage-reward", "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "arrow", "version": 1, "versionNonce": 512, "isDeleted": false, "id": "edge-reward-cue", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 820, "y": 268, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 600, "height": 140, "seed": 512, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [-300, -120], [-600, 0]], "lastCommittedPoint": null, "startBinding": {"elementId": "stage-reward", "focus": 0, "gap": 0}, "endBinding": {"elementId": "stage-cue", "focus": 0, "gap": 0}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 601, "isDeleted": false, "id": "law-obvious", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 220, "y": 340, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 150, "height": 44, "seed": 601, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-law-obvious"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 602, "isDeleted": false, "id": "text-law-obvious", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 240, "y": 352, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 110, "height": 22, "seed": 602, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 16, "fontFamily": 1, "text": "Make it obvious", "textAlign": "center", "verticalAlign": "middle", "containerId": "law-obvious", "originalText": "Make it obvious", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 603, "isDeleted": false, "id": "edge-obvious-cue", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 295, "y": 336, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 20, "seed": 603, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, -20]], "lastCommittedPoint": null, "startBinding": {"elementId": "law-obvious", "focus": 0, "gap": 4}, "endBinding": {"elementId": "stage-cue", "focus": 0, "gap": 2}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 604, "isDeleted": false, "id": "law-attractive", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 420, "y": 340, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 150, "height": 44, "seed": 604, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-law-attractive"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 605, "isDeleted": false, "id": "text-law-attractive", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 440, "y": 352, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 110, "height": 22, "seed": 605, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 16, "fontFamily": 1, "text": "Make it attractive", "textAlign": "center", "verticalAlign": "middle", "containerId": "law-attractive", "originalText": "Make it attractive", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 606, "isDeleted": false, "id": "edge-attractive-craving", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 495, "y": 336, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 20, "seed": 606, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, -20]], "lastCommittedPoint": null, "startBinding": {"elementId": "law-attractive", "focus": 0, "gap": 4}, "endBinding": {"elementId": "stage-craving", "focus": 0, "gap": 2}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 607, "isDeleted": false, "id": "law-easy", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 620, "y": 340, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 150, "height": 44, "seed": 607, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-law-easy"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 608, "isDeleted": false, "id": "text-law-easy", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 640, "y": 352, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 110, "height": 22, "seed": 608, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 16, "fontFamily": 1, "text": "Make it easy", "textAlign": "center", "verticalAlign": "middle", "containerId": "law-easy", "originalText": "Make it easy", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 609, "isDeleted": false, "id": "edge-easy-response", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 695, "y": 336, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 20, "seed": 609, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, -20]], "lastCommittedPoint": null, "startBinding": {"elementId": "law-easy", "focus": 0, "gap": 4}, "endBinding": {"elementId": "stage-response", "focus": 0, "gap": 2}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 610, "isDeleted": false, "id": "law-satisfying", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 820, "y": 340, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 150, "height": 44, "seed": 610, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-law-satisfying"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 611, "isDeleted": false, "id": "text-law-satisfying", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 840, "y": 352, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 110, "height": 22, "seed": 611, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 16, "fontFamily": 1, "text": "Make it satisfying", "textAlign": "center", "verticalAlign": "middle", "containerId": "law-satisfying", "originalText": "Make it satisfying", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 612, "isDeleted": false, "id": "edge-satisfying-reward", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dotted", "roughness": 1, "opacity": 100, "angle": 0, "x": 895, "y": 336, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 20, "seed": 612, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, -20]], "lastCommittedPoint": null, "startBinding": {"elementId": "law-satisfying", "focus": 0, "gap": 4}, "endBinding": {"elementId": "stage-reward", "focus": 0, "gap": 2}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 701, "isDeleted": false, "id": "node-ifthen", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 600, "y": 410, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 190, "height": 48, "seed": 701, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-ifthen"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 702, "isDeleted": false, "id": "text-ifthen", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 620, "y": 424, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 150, "height": 22, "seed": 702, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 16, "fontFamily": 1, "text": "Implementation Intention", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-ifthen", "originalText": "Implementation Intention", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 703, "isDeleted": false, "id": "edge-ifthen-response", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 695, "y": 410, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 66, "seed": 703, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, -66]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-ifthen", "focus": 0, "gap": 0}, "endBinding": {"elementId": "stage-response", "focus": 0, "gap": 2}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 704, "isDeleted": false, "id": "node-progress-fulfilling", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 800, "y": 410, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 190, "height": 48, "seed": 704, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-progress-fulfilling"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 705, "isDeleted": false, "id": "text-progress-fulfilling", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 820, "y": 424, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 150, "height": 22, "seed": 705, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 16, "fontFamily": 1, "text": "让进度更有满足感", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-progress-fulfilling", "originalText": "让进度更有满足感", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 706, "isDeleted": false, "id": "edge-fulfilling-reward", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 895, "y": 410, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 0, "height": 66, "seed": 706, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, -66]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-progress-fulfilling", "focus": 0, "gap": 0}, "endBinding": {"elementId": "stage-reward", "focus": 0, "gap": 2}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "rectangle", "version": 1, "versionNonce": 707, "isDeleted": false, "id": "node-clarity", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 520, "y": 410, "strokeColor": "#0ea5a4", "backgroundColor": "#ffffff", "width": 70, "height": 48, "seed": 707, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-clarity"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 708, "isDeleted": false, "id": "text-clarity", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 530, "y": 424, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 50, "height": 22, "seed": 708, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 16, "fontFamily": 1, "text": "清晰", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-clarity", "originalText": "清晰", "lineHeight": 1.25}, {"type": "rectangle", "version": 1, "versionNonce": 709, "isDeleted": false, "id": "node-barrier", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 740, "y": 410, "strokeColor": "#ef4444", "backgroundColor": "#ffffff", "width": 70, "height": 48, "seed": 709, "groupIds": [], "roundness": {"type": 3}, "boundElements": [{"type": "text", "id": "text-barrier"}], "updated": 1}, {"type": "text", "version": 1, "versionNonce": 710, "isDeleted": false, "id": "text-barrier", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 750, "y": 424, "strokeColor": "#ef4444", "backgroundColor": "transparent", "width": 50, "height": 22, "seed": 710, "groupIds": [], "roundness": null, "updated": 1, "fontSize": 16, "fontFamily": 1, "text": "障碍", "textAlign": "center", "verticalAlign": "middle", "containerId": "node-barrier", "originalText": "障碍", "lineHeight": 1.25}, {"type": "arrow", "version": 1, "versionNonce": 711, "isDeleted": false, "id": "edge-clarity-response", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 555, "y": 410, "strokeColor": "#0ea5a4", "backgroundColor": "transparent", "width": 120, "height": 70, "seed": 711, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [65, -66], [120, -70]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-clarity", "focus": 0, "gap": 0}, "endBinding": {"elementId": "stage-response", "focus": 0, "gap": 2}, "startArrowhead": null, "endArrowhead": "arrow"}, {"type": "arrow", "version": 1, "versionNonce": 712, "isDeleted": false, "id": "edge-barrier-response", "fillStyle": "hachure", "strokeWidth": 2, "strokeStyle": "dashed", "roughness": 1, "opacity": 100, "angle": 0, "x": 740, "y": 410, "strokeColor": "#ef4444", "backgroundColor": "transparent", "width": 0, "height": 66, "seed": 712, "groupIds": [], "roundness": null, "updated": 1, "points": [[0, 0], [0, -66]], "lastCommittedPoint": null, "startBinding": {"elementId": "node-barrier", "focus": 0, "gap": 0}, "endBinding": {"elementId": "stage-response", "focus": 0, "gap": 2}, "startArrowhead": null, "endArrowhead": "arrow"}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff", "currentItemFontFamily": 1}, "files": {}}